 function toggleMobileMenu() {
            const overlay = document.getElementById('mobile-menu-overlay');
            const menu = document.getElementById('mobile-menu');
            
            if (overlay.classList.contains('hidden')) {
                overlay.classList.remove('hidden');
                setTimeout(() => {
                    menu.classList.remove('-translate-x-full');
                }, 10);
            } else {
                menu.classList.add('-translate-x-full');
                setTimeout(() => {
                    overlay.classList.add('hidden');
                }, 300);
            }
        }

       

        function downloadItinerary() {
            // Check if tour data is available
            if (typeof tourData === 'undefined') {
                alert('Tour data not available. Please refresh the page and try again.');
                return;
            }

            // Check if pdfLink exists and is not empty
            if (!tourData.pdfLink || tourData.pdfLink.trim() === '') {
                alert('PDF itinerary is not available for this tour.');
                return;
            }

            // Create a temporary link element and trigger download
            const link = document.createElement('a');
            link.href = tourData.pdfLink;
            link.download = `${tourData.title || 'Tour'}_Itinerary.pdf`;
            link.target = '_blank'; // Open in new tab as fallback

            // Append to body, click, and remove
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        function shareTrip() {
            if (navigator.share) {
                navigator.share({
                    title: 'Visit Pakistan Trip',
                    text: 'Check out my amazing trip to Pakistan!',
                    url: window.location.href
                });
            } else {
                // Fallback for browsers that don't support Web Share API
                const url = window.location.href;
                navigator.clipboard.writeText(url).then(() => {
                    alert('Trip link copied to clipboard!');
                });
            }
        }

        function showDestinationDetails(id, title, text, videoLink) {
            // Set modal content
            document.getElementById('modalTitle').textContent = title;
            document.getElementById('modalText').textContent = text;

            // Handle video
            const videoSection = document.getElementById('videoSection');
            const videoIframe = document.getElementById('destinationVideo');

            if (videoLink && videoLink.trim() !== '' && videoLink !== 'null') {
                videoIframe.src = videoLink;
                videoSection.style.display = 'block';
            } else {
                videoIframe.src = '';
                videoSection.style.display = 'none';
            }

            // Show modal
            document.getElementById('destinationModal').classList.remove('hidden');
        }

        function closeDestinationModal() {
            // Hide modal
            document.getElementById('destinationModal').classList.add('hidden');

            // Stop video playback by clearing src
            document.getElementById('destinationVideo').src = '';
        }



        // Close mobile menu when clicking overlay
        document.getElementById('mobile-menu-overlay').addEventListener('click', function(e) {
            if (e.target === this) {
                toggleMobileMenu();
            }
        });

        // Close destination modal when clicking overlay
        document.getElementById('destinationModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeDestinationModal();
            }
        });

