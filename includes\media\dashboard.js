     function toggleMobileMenu() {
            const overlay = document.getElementById('mobile-menu-overlay');
            const menu = document.getElementById('mobile-menu');
            
            if (overlay.classList.contains('hidden')) {
                overlay.classList.remove('hidden');
                setTimeout(() => {
                    menu.classList.remove('-translate-x-full');
                }, 10);
            } else {
                menu.classList.add('-translate-x-full');
                setTimeout(() => {
                    overlay.classList.add('hidden');
                }, 300);
            }
        }

        function showNewTourModal() {
            document.getElementById('newTourModal').classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }

        function closeNewTourModal() {
            document.getElementById('newTourModal').classList.add('hidden');
            document.body.style.overflow = 'auto';
        }

        async function handleNewTour(event) {
            event.preventDefault();
            const form = event.target;
            const formData = new FormData(form);

            try {
                // Create new tour
                const newTourResponse = await fetch('api/new-tour.php', {
                    method: 'POST',
                    body: formData
                });

                if (!newTourResponse.ok) {
                    throw new Error('Failed to create tour');
                }

                const newTourData = await newTourResponse.json();
                if (!newTourData.success) {
                    throw new Error(newTourData.message || 'Failed to create tour');
                }

                // Add tour
                const addTourResponse = await fetch('api/add-tour.php', {
                    method: 'POST',
                    body: JSON.stringify({
                        tourId: newTourData.response
                    })
                });

                if (!addTourResponse.ok) {
                    throw new Error('Failed to add tour');
                }

                const addTourData = await addTourResponse.json();
                if (!addTourData.success) {
                    throw new Error(addTourData.message || 'Failed to add tour');
                }

                showNewTourModal();
                closeNewTourModal();
                viewTourPlan(addTourData.response);

            } catch (error) {
                console.error('Error:', error);
                alert('Error: ' + error.message);
            }
        }

        function viewTourPlan(tourId) {
            window.location.href = `tour-plan.php?tour=${tourId}`;
        }

        
        // Close mobile menu when clicking overlay
        document.getElementById('mobile-menu-overlay').addEventListener('click', function(e) {
            if (e.target === this) {
                toggleMobileMenu();
            }
        });

        // Close modal when clicking outside
        window.onclick = function(event) {
            const modal = document.getElementById('newTourModal');
            if (event.target === modal) {
                closeNewTourModal();
            }
        }

        // Check authentication
       