<?php
include '../includes/config.php';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    //sanitize inputs
    $fullname = filter_input(INPUT_POST, 'fullname', FILTER_SANITIZE_STRING);
    $email = filter_input(INPUT_POST, 'email', FILTER_SANITIZE_EMAIL);
    $password = filter_input(INPUT_POST, 'password', FILTER_SANITIZE_STRING);

    //check if email already exists in db
    $sql = "SELECT * FROM user WHERE email = '$email'";
    $result = $db->query($sql);
    if ($result->num_rows > 0) {
        $data = array(
            'success' => false,
            'message' => 'Email already exists'
        );
        echo json_encode($data);
        exit;
    }

    // hash password
    $hashed_password = password_hash($password, PASSWORD_DEFAULT);

    // insert into db
    $sql = "INSERT INTO user (fullname, email, password) VALUES ('$fullname', '$email', '$hashed_password')";
    $result = $db->query($sql);

    if ($result) {
        // store user id in session
        $_SESSION['user_id'] = $db->insert_id;
        $_SESSION['fullname'] = $fullname;
        $_SESSION['email'] = $email;
        $data = array(
            'success' => true
        );
        echo json_encode($data);
    } else {
        $data = array(
            'success' => false,
            'message' => 'Failed to create account. Please try again.'
        );
        echo json_encode($data);
    }
}
?>