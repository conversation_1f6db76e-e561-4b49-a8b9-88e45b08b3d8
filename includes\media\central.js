        //logout
        function logout() {
            // request to api/logout.php
            fetch('api/logout.php', {
                method: 'POST',
                headers: {
                    'Accept': 'application/json'
                }
            })
            // .then(response => {
            //     if (!response.ok) {
            //         throw new Error('Network response was not ok');
            //     }
            //     return response.json();
            // })
            // .then(data => {
            //     if (data.success) {
            //     } else {
            //         throw new Error(data.message || 'Failed to logout');
            //     }
            // })
            // .catch(error => {
            //     console.error('Error:', error);
            //     alert('Error: ' + error.message);
            // });

        }