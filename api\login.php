<?php
include '../includes/config.php';
// password from input is text and in db is in passowrd_hash format
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    //sanitize inputs
    $email = filter_input(INPUT_POST, 'email', FILTER_SANITIZE_EMAIL);
    $password = filter_input(INPUT_POST, 'password', FILTER_SANITIZE_STRING);
    
    //check if email exists in db
    $sql = "SELECT * FROM user WHERE email = '$email'";
    $result = $db->query($sql);
    if ($result->num_rows > 0) {
        $user = $result->fetch_assoc(); // Fetch user data once and store it
        $db_password = $user['password'];
        if(password_verify($password, $db_password)){
        // check if password matches using password_verify In Shaa Allah
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['fullname'] = $user['fullname'];
        $_SESSION['email'] = $user['email'];
        $data = array(
            'success' => true
        );
        echo json_encode($data);
    }else{
        $data = array(
            'success' => false,
            'message' => 'Invalid email or password'
        );
        echo json_encode($data);
        exit;
    }
      
    }else{
        $data = array(
            'success' => false,
            'message' => 'Please Create An Account First'
        );
        echo json_encode($data);
        exit;
    }

}
?>