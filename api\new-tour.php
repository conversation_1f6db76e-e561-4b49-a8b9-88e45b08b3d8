<?php
include '../includes/config.php';
// password from input is text and in db is in passowrd_hash format
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    //sanitize inputs
    $text = $_POST['text'];
    //fetch tours from db In Shaa Allah
    
    // Get user ID from session
    $userId = $_SESSION['user_id'];

    // First try to fetch tours not in user's history
    $sql = "SELECT t.*, GROUP_CONCAT(d.title ORDER BY d.rank DESC SEPARATOR ' • ') as destinations 
            FROM tour t 
            LEFT JOIN destinations d ON t.id = d.tid 
            WHERE t.id NOT IN (SELECT tid FROM usertours WHERE uid = ?)
            GROUP BY t.id";
    $stmt = $db->prepare($sql);
    $stmt->bind_param("i", $userId);
    $stmt->execute();
    $result = $stmt->get_result();

    // If no results found, fetch any tour
    if ($result->num_rows == 0) {
        $sql = "SELECT t.*, GROUP_CONCAT(d.title ORDER BY d.rank DESC SEPARATOR ' • ') as destinations 
                FROM tour t 
                LEFT JOIN destinations d ON t.id = d.tid 
                GROUP BY t.id
                LIMIT 1";
        $result = $db->query($sql);
    }
    
    // Build tours information string
    $toursInfo = "Available tours:\n";
    while($tour = $result->fetch_assoc()) {
        $toursInfo .= "- {$tour['title']}, id {$tour['id']} ({$tour['duration']} days, {$tour['persons']} persons, $" . $tour['cost'] . "/person)\n";
        $toursInfo .= "  Destinations: {$tour['destinations']}\n";
    }

    // Prepare data for the API request
    $data = array(
        'model' => 'mistral-ai/Mistral-Large-2411',
        'messages' => array(
            array(
                'role' => 'system',
                'content' => "You are a helpful travel assistant that helps plan tours in Pakistan. Here are the current available tours:\n\n$toursInfo\n\nPlease suggest the most suitable tour based on user requirements. In Shaa Allah.return the id of the tour only no other text "
            ),
            array(
                'role' => 'user',
                'content' => $text
            )
        ),
        'temperature' => 0.2,
        'top_p' => 1
    );

    // Initialize cURL session
    $ch = curl_init('https://models.github.ai/inference/chat/completions');
    
    // Set cURL options
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_HTTPHEADER, array(
        'Content-Type: application/json',
        'Authorization: Bearer ****************************************' 
    ));

    // Execute cURL request
    $response = curl_exec($ch);
    
    // Check for errors
    if(curl_errno($ch)) {
        echo json_encode(array(
            'success' => false,
            'message' => 'Curl error: ' . curl_error($ch)
        ));
        exit;
    }
    
    curl_close($ch);

    // Parse response and return only the first choice message
    $responseData = json_decode($response, true);
    $message = isset($responseData['choices'][0]['message']['content']) 
        ? $responseData['choices'][0]['message']['content'] 
        : 'No response generated';

    // Return response
    echo json_encode(array(
        'success' => true,
        'response' => $message
    ));

}
?>