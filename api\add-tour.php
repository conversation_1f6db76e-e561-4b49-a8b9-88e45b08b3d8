<?php
include '../includes/config.php';
// get and sinative tourId
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get JSON data
    $data = json_decode(file_get_contents('php://input'), true);
    $tourId = isset($data['tourId']) ? intval($data['tourId']) : 0;

    if ($tourId > 0) {
        // Check if user is logged in
        if (!isset($_SESSION['user_id'])) {
            echo json_encode(['success' => false, 'message' => 'User not logged in']);
            exit;
        }

        // Insert new tour for user
        $userId = $_SESSION['user_id'];
        $sql = "INSERT INTO usertours (uid, tid) VALUES (?, ?)";
        $stmt = $db->prepare($sql);
        $stmt->bind_param("ii", $userId, $tourId);
        
        if ($stmt->execute()) {
            echo json_encode(['success' => true, 'response' => $tourId]);
        } else {
            echo json_encode(['success' => false, 'message' => 'Failed to add tour']);
        }
        $stmt->close();
    } else {
        echo json_encode(['success' => false, 'message' => 'Invalid tour ID']);
    }
}
    