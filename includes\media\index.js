 
        function showLogin() {
            document.getElementById('loginModal').classList.remove('hidden');
            document.getElementById('signupModal').classList.add('hidden');
        }

        function showSignup() {
            document.getElementById('signupModal').classList.remove('hidden');
            document.getElementById('loginModal').classList.add('hidden');
        }

        function closeModal() {
            document.getElementById('loginModal').classList.add('hidden');
            document.getElementById('signupModal').classList.add('hidden');
        }

        function handleLogin(event) {
            event.preventDefault();
            
            const form = event.target;
            const formData = new FormData(form);

            fetch('api/login.php', {
                method: 'POST',
                body: formData,
                headers: {
                    'Accept': 'application/json'
                }
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    alert('Login successful! Redirecting to dashboard...');
                    window.location.href = 'dashboard.php';  // Changed to .php extension
                } else {
                    throw new Error(data.message || 'Failed to login');
                }
            })
            .catch(error => {
                console.error('', error);
                alert('' + error.message);
            });
        }



        function handleSignup(event) {
            event.preventDefault();
            
            const form = event.target;
            const formData = new FormData(form);

            fetch('api/register.php', {
                method: 'POST',
                body: formData,
                headers: {
                    'Accept': 'application/json'
                }
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    alert('Account created successfully! Redirecting to dashboard...');
                    window.location.href = 'dashboard.php';  // Changed to .php extension
                } else {
                    throw new Error(data.message || 'Failed to create account');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error: ' + error.message);
            });
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            const loginModal = document.getElementById('loginModal');
            const signupModal = document.getElementById('signupModal');
            if (event.target === loginModal) {
                loginModal.classList.add('hidden');
            }
            if (event.target === signupModal) {
                signupModal.classList.add('hidden');
            }
        }
    